// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'draft_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DraftState _$DraftStateFromJson(Map<String, dynamic> json) => DraftState(
      currentPhase: $enumDecode(_$DraftPhaseEnumMap, json['currentPhase']),
      currentTeam: $enumDecode(_$TeamEnumMap, json['currentTeam']),
      currentPlayer: (json['currentPlayer'] as num).toInt(),
      mapName: json['mapName'] as String,
      teamABans: (json['teamABans'] as List<dynamic>?)
              ?.map((e) => Brawler.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      teamBBans: (json['teamBBans'] as List<dynamic>?)
              ?.map((e) => Brawler.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      teamAPicks: (json['teamAPicks'] as List<dynamic>?)
              ?.map((e) => Brawler.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      teamBPicks: (json['teamBPicks'] as List<dynamic>?)
              ?.map((e) => Brawler.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      availableBrawlers: (json['availableBrawlers'] as List<dynamic>?)
              ?.map((e) => Brawler.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isPlayerTeamA: json['isPlayerTeamA'] as bool? ?? true,
      timeRemaining: (json['timeRemaining'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$DraftStateToJson(DraftState instance) =>
    <String, dynamic>{
      'currentPhase': _$DraftPhaseEnumMap[instance.currentPhase]!,
      'currentTeam': _$TeamEnumMap[instance.currentTeam]!,
      'currentPlayer': instance.currentPlayer,
      'mapName': instance.mapName,
      'teamABans': instance.teamABans,
      'teamBBans': instance.teamBBans,
      'teamAPicks': instance.teamAPicks,
      'teamBPicks': instance.teamBPicks,
      'availableBrawlers': instance.availableBrawlers,
      'isPlayerTeamA': instance.isPlayerTeamA,
      'timeRemaining': instance.timeRemaining,
    };

const _$DraftPhaseEnumMap = {
  DraftPhase.banPhase: 'banPhase',
  DraftPhase.pickPhase: 'pickPhase',
  DraftPhase.completed: 'completed',
};

const _$TeamEnumMap = {
  Team.teamA: 'teamA',
  Team.teamB: 'teamB',
};
