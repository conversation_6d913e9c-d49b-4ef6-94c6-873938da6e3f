import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../core/services/permission_service.dart';
import '../../core/services/overlay_service.dart';
import '../models/draft_state.dart';
import '../models/brawler.dart';
import '../models/recommendation.dart';

// Service Providers
final permissionServiceProvider = Provider<PermissionService>((ref) {
    return PermissionService();
});

final overlayServiceProvider = Provider<OverlayService>((ref) {
    return OverlayService();
});

// Permission State Providers
final hasOverlayPermissionProvider = FutureProvider<bool>((ref) async {
    final permissionService = ref.read(permissionServiceProvider);
    return await permissionService.hasOverlayPermission();
});

final hasAllPermissionsProvider = FutureProvider<bool>((ref) async {
    final permissionService = ref.read(permissionServiceProvider);
    return await permissionService.hasAllPermissions();
});

final permissionStatusesProvider = FutureProvider<Map<String, PermissionStatus>>((ref) async {
    final permissionService = ref.read(permissionServiceProvider);
    return await permissionService.getAllPermissionStatuses();
});

// Overlay State Providers
final overlayStatusProvider = StreamProvider<bool>((ref) {
    final overlayService = ref.read(overlayServiceProvider);
    return overlayService.overlayStatusStream;
});

final isOverlayActiveProvider = Provider<bool>((ref) {
    final overlayService = ref.read(overlayServiceProvider);
    return overlayService.isOverlayActive;
});

final overlayInfoProvider = FutureProvider<Map<String, dynamic>>((ref) async {
    final overlayService = ref.read(overlayServiceProvider);
    return await overlayService.getOverlayStatus();
});

// Draft State Providers
final currentDraftStateProvider = StateNotifierProvider<DraftStateNotifier, DraftState?>((ref) {
    return DraftStateNotifier();
});

final availableBrawlersProvider = StateNotifierProvider<BrawlerListNotifier, List<Brawler>>((ref) {
    return BrawlerListNotifier();
});

final currentRecommendationsProvider = StateNotifierProvider<RecommendationNotifier, RecommendationSet?>((ref) {
    return RecommendationNotifier();
});

// App State Providers
final isAppInitializedProvider = StateProvider<bool>((ref) => false);

final appErrorProvider = StateProvider<String?>((ref) => null);

final isLoadingProvider = StateProvider<bool>((ref) => false);

// Settings Providers
final overlayPositionProvider = StateProvider<String>((ref) => 'topRight');

final detectionSensitivityProvider = StateProvider<double>((ref) => 0.8);

final enableAutoDetectionProvider = StateProvider<bool>((ref) => true);

final enableNotificationsProvider = StateProvider<bool>((ref) => true);

// State Notifiers
class DraftStateNotifier extends StateNotifier<DraftState?> {
    DraftStateNotifier() : super(null);

    void updateDraftState(DraftState newState) {
        state = newState;
    }

    void resetDraftState() {
        state = null;
    }

    void updateCurrentPhase(DraftPhase phase) {
        if (state != null) {
            state = state!.copyWith(currentPhase: phase);
        }
    }

    void updateCurrentTeam(Team team) {
        if (state != null) {
            state = state!.copyWith(currentTeam: team);
        }
    }

    void addBan(Brawler brawler, Team team) {
        if (state != null) {
            if (team == Team.teamA) {
                final newBans = [...state!.teamABans, brawler];
                state = state!.copyWith(teamABans: newBans);
            } else {
                final newBans = [...state!.teamBBans, brawler];
                state = state!.copyWith(teamBBans: newBans);
            }
        }
    }

    void addPick(Brawler brawler, Team team) {
        if (state != null) {
            if (team == Team.teamA) {
                final newPicks = [...state!.teamAPicks, brawler];
                state = state!.copyWith(teamAPicks: newPicks);
            } else {
                final newPicks = [...state!.teamBPicks, brawler];
                state = state!.copyWith(teamBPicks: newPicks);
            }
        }
    }

    void updateTimeRemaining(int seconds) {
        if (state != null) {
            state = state!.copyWith(timeRemaining: seconds);
        }
    }

    void initializeDraft({
        required String mapName,
        required List<Brawler> allBrawlers,
        bool isPlayerTeamA = true,
    }) {
        state = DraftState.initial(
            mapName: mapName,
            allBrawlers: allBrawlers,
            isPlayerTeamA: isPlayerTeamA,
        );
    }
}

class BrawlerListNotifier extends StateNotifier<List<Brawler>> {
    BrawlerListNotifier() : super([]);

    void updateBrawlers(List<Brawler> brawlers) {
        state = brawlers;
    }

    void addBrawler(Brawler brawler) {
        state = [...state, brawler];
    }

    void removeBrawler(String brawlerId) {
        state = state.where((b) => b.id != brawlerId).toList();
    }

    void updateBrawlerStats(String brawlerId, {
        double? winRate,
        double? pickRate,
        double? banRate,
    }) {
        state = state.map((brawler) {
            if (brawler.id == brawlerId) {
                return brawler.copyWith(
                    winRate: winRate ?? brawler.winRate,
                    pickRate: pickRate ?? brawler.pickRate,
                    banRate: banRate ?? brawler.banRate,
                );
            }
            return brawler;
        }).toList();
    }

    void loadDefaultBrawlers() {
        // This would typically load from assets or API
        // For now, we'll create a few sample brawlers
        final sampleBrawlers = [
            const Brawler(
                id: 'shelly',
                name: 'Shelly',
                rarity: BrawlerRarity.common,
                type: BrawlerType.fighter,
                iconPath: 'assets/images/brawlers/shelly.png',
                winRate: 52.3,
                pickRate: 8.7,
                banRate: 2.1,
            ),
            const Brawler(
                id: 'colt',
                name: 'Colt',
                rarity: BrawlerRarity.rare,
                type: BrawlerType.marksman,
                iconPath: 'assets/images/brawlers/colt.png',
                winRate: 48.9,
                pickRate: 6.2,
                banRate: 1.8,
            ),
            const Brawler(
                id: 'bull',
                name: 'Bull',
                rarity: BrawlerRarity.rare,
                type: BrawlerType.tank,
                iconPath: 'assets/images/brawlers/bull.png',
                winRate: 54.1,
                pickRate: 7.3,
                banRate: 3.2,
            ),
        ];
        
        state = sampleBrawlers;
    }
}

class RecommendationNotifier extends StateNotifier<RecommendationSet?> {
    RecommendationNotifier() : super(null);

    void updateRecommendations(RecommendationSet recommendations) {
        state = recommendations;
    }

    void clearRecommendations() {
        state = null;
    }

    void generateSampleRecommendations() {
        // Generate sample recommendations for testing
        final samplePicks = [
            const Recommendation(
                brawler: Brawler(
                    id: 'edgar',
                    name: 'Edgar',
                    rarity: BrawlerRarity.epic,
                    type: BrawlerType.assassin,
                    iconPath: 'assets/images/brawlers/edgar.png',
                ),
                score: 87.5,
                reasons: ['High win rate on this map', 'Counters enemy team composition'],
                type: RecommendationType.pick,
                priority: RecommendationPriority.high,
            ),
        ];

        final sampleBans = [
            const Recommendation(
                brawler: Brawler(
                    id: 'mortis',
                    name: 'Mortis',
                    rarity: BrawlerRarity.mythic,
                    type: BrawlerType.assassin,
                    iconPath: 'assets/images/brawlers/mortis.png',
                ),
                score: 92.1,
                reasons: ['Dominant on this map', 'Hard counter to your team'],
                type: RecommendationType.ban,
                priority: RecommendationPriority.critical,
            ),
        ];

        state = RecommendationSet(
            picks: samplePicks,
            bans: sampleBans,
            generatedAt: DateTime.now(),
            mapSpecificNotes: ['This map favors close-range brawlers'],
            teamCompositionNotes: ['Consider adding a support brawler'],
        );
    }
}
