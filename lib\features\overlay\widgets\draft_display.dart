import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../core/constants/app_theme.dart';
import '../../../shared/models/draft_state.dart';
import '../../../shared/models/brawler.dart';

/// Widget that displays the current draft state
class DraftDisplay extends StatelessWidget {
    const DraftDisplay({
        super.key,
        required this.draftState,
        required this.onBrawlerTap,
    });

    final DraftState? draftState;
    final Function(String) onBrawlerTap;

    @override
    Widget build(BuildContext context) {
        if (draftState == null) {
            return _buildWaitingState();
        }

        return Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
                children: [
                    // Map name
                    _buildMapHeader(),
                    const SizedBox(height: 12),
                    
                    // Teams display
                    Expanded(
                        child: Row(
                            children: [
                                // Team A
                                Expanded(
                                    child: _buildTeamColumn(
                                        'Team A',
                                        draftState!.teamAPicks,
                                        draftState!.teamABans,
                                        draftState!.isPlayerTeamA,
                                        draftState!.currentTeam == Team.teamA,
                                    ),
                                ),
                                
                                // Divider
                                Container(
                                    width: 1,
                                    color: AppTheme.overlayBorderColor,
                                    margin: const EdgeInsets.symmetric(horizontal: 8),
                                ),
                                
                                // Team B
                                Expanded(
                                    child: _buildTeamColumn(
                                        'Team B',
                                        draftState!.teamBPicks,
                                        draftState!.teamBBans,
                                        !draftState!.isPlayerTeamA,
                                        draftState!.currentTeam == Team.teamB,
                                    ),
                                ),
                            ],
                        ),
                    ),
                    
                    // Phase indicator
                    _buildPhaseIndicator(),
                ],
            ),
        );
    }

    /// Build waiting state when no draft is active
    Widget _buildWaitingState() {
        return Center(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                    Icon(
                        MdiIcons.clockOutline,
                        color: AppTheme.secondaryTextColor,
                        size: 48,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                        'Waiting for draft to start...',
                        style: TextStyle(
                            color: AppTheme.secondaryTextColor,
                            fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                        'Enter a ranked match in Brawl Stars',
                        style: TextStyle(
                            color: AppTheme.disabledTextColor,
                            fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                    ),
                ],
            ),
        );
    }

    /// Build map header
    Widget _buildMapHeader() {
        return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                    Icon(
                        MdiIcons.map,
                        color: AppTheme.primaryColor,
                        size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                        draftState?.mapName ?? 'Unknown Map',
                        style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                        ),
                    ),
                ],
            ),
        );
    }

    /// Build team column showing picks and bans
    Widget _buildTeamColumn(
        String teamName,
        List<Brawler> picks,
        List<Brawler> bans,
        bool isPlayerTeam,
        bool isCurrentTeam,
    ) {
        return Column(
            children: [
                // Team header
                Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                        color: isCurrentTeam 
                            ? AppTheme.primaryColor.withValues(alpha: 0.2)
                            : AppTheme.surfaceColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: isPlayerTeam 
                            ? Border.all(color: AppTheme.successColor, width: 1)
                            : null,
                    ),
                    child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                            if (isPlayerTeam)
                                Icon(
                                    MdiIcons.account,
                                    color: AppTheme.successColor,
                                    size: 12,
                                ),
                            if (isPlayerTeam) const SizedBox(width: 4),
                            Text(
                                teamName,
                                style: TextStyle(
                                    color: isCurrentTeam 
                                        ? AppTheme.primaryColor
                                        : AppTheme.primaryTextColor,
                                    fontSize: 11,
                                    fontWeight: FontWeight.w600,
                                ),
                            ),
                        ],
                    ),
                ),
                const SizedBox(height: 8),
                
                // Picks section
                _buildBrawlerSection('Picks', picks, true),
                const SizedBox(height: 8),
                
                // Bans section
                _buildBrawlerSection('Bans', bans, false),
            ],
        );
    }

    /// Build brawler section (picks or bans)
    Widget _buildBrawlerSection(String title, List<Brawler> brawlers, bool isPicks) {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                Text(
                    title,
                    style: TextStyle(
                        color: AppTheme.secondaryTextColor,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                    ),
                ),
                const SizedBox(height: 4),
                
                // Brawler slots
                SizedBox(
                    height: 60,
                    child: Row(
                        children: List.generate(3, (index) {
                            final brawler = index < brawlers.length ? brawlers[index] : null;
                            return Expanded(
                                child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 1),
                                    child: _buildBrawlerSlot(brawler, isPicks),
                                ),
                            );
                        }),
                    ),
                ),
            ],
        );
    }

    /// Build individual brawler slot
    Widget _buildBrawlerSlot(Brawler? brawler, bool isPicks) {
        return GestureDetector(
            onTap: brawler != null ? () => onBrawlerTap(brawler.id) : null,
            child: Container(
                decoration: BoxDecoration(
                    color: brawler != null 
                        ? (isPicks ? AppTheme.successColor : AppTheme.errorColor).withValues(alpha: 0.1)
                        : AppTheme.surfaceColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                        color: brawler != null 
                            ? (isPicks ? AppTheme.successColor : AppTheme.errorColor).withValues(alpha: 0.3)
                            : AppTheme.overlayBorderColor,
                        width: 1,
                    ),
                ),
                child: brawler != null 
                    ? _buildBrawlerContent(brawler, isPicks)
                    : _buildEmptySlot(isPicks),
            ),
        );
    }

    /// Build brawler content
    Widget _buildBrawlerContent(Brawler brawler, bool isPicks) {
        return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
                // Brawler icon placeholder
                Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                        color: AppTheme.getRarityColor(brawler.rarity.displayName),
                        shape: BoxShape.circle,
                    ),
                    child: Icon(
                        MdiIcons.account,
                        color: Colors.white,
                        size: 16,
                    ),
                ),
                const SizedBox(height: 2),
                
                // Brawler name
                Text(
                    brawler.name,
                    style: const TextStyle(
                        color: AppTheme.primaryTextColor,
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                ),
            ],
        );
    }

    /// Build empty slot
    Widget _buildEmptySlot(bool isPicks) {
        return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
                Icon(
                    isPicks ? MdiIcons.plus : MdiIcons.close,
                    color: AppTheme.disabledTextColor,
                    size: 16,
                ),
                const SizedBox(height: 2),
                Text(
                    isPicks ? 'Pick' : 'Ban',
                    style: const TextStyle(
                        color: AppTheme.disabledTextColor,
                        fontSize: 8,
                    ),
                ),
            ],
        );
    }

    /// Build phase indicator
    Widget _buildPhaseIndicator() {
        return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
                color: AppTheme.warningColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                    Icon(
                        draftState!.currentPhase == DraftPhase.banPhase 
                            ? MdiIcons.close 
                            : MdiIcons.plus,
                        color: AppTheme.warningColor,
                        size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                        '${draftState!.currentPhase.displayName} - ${draftState!.currentTeam.displayName}',
                        style: TextStyle(
                            color: AppTheme.warningColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                        ),
                    ),
                ],
            ),
        );
    }
}
