import 'dart:async';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
// ...existing code...

import '../../main.dart';
import '../constants/app_constants.dart';
import 'permission_service.dart';

/// Service for managing the floating overlay window
class OverlayService {
  static final OverlayService _instance = OverlayService._internal();
  factory OverlayService() => _instance;
  OverlayService._internal();

  final PermissionService _permissionService = PermissionService();

  bool _isOverlayActive = false;
  StreamController<bool>? _overlayStatusController;

  /// Stream to listen to overlay status changes
  Stream<bool> get overlayStatusStream {
    _overlayStatusController ??= StreamController<bool>.broadcast();
    return _overlayStatusController!.stream;
  }

  /// Check if overlay is currently active
  bool get isOverlayActive => _isOverlayActive;

  /// Start the floating overlay
  Future<bool> startOverlay() async {
    try {
      logger.i('Starting overlay service...');

      // Check permissions first
      if (!await _permissionService.hasOverlayPermission()) {
        logger.w('Cannot start overlay: permission not granted');
        return false;
      }

      // Check if overlay is already active
      if (_isOverlayActive) {
        logger.w('Overlay is already active');
        return true;
      }

      // Configure overlay settings
      final overlayFlag = OverlayFlag.defaultFlag;
      final overlayAlignment = OverlayAlignment.topRight;

      // Start the overlay
      await FlutterOverlayWindow.showOverlay(
        flag: overlayFlag,
        alignment: overlayAlignment,
        width: AppConstants.overlayIconSize.toInt(),
        height: AppConstants.overlayIconSize.toInt(),
        enableDrag: true,
      );
      _isOverlayActive = true;
      _notifyOverlayStatusChange(true);
      logger.i('Overlay started successfully');
      return true;
    } catch (e) {
      logger.e('Error starting overlay: $e');
      return false;
    }
  }

  /// Stop the floating overlay
  Future<bool> stopOverlay() async {
    try {
      logger.i('Stopping overlay service...');

      if (!_isOverlayActive) {
        logger.w('Overlay is not active');
        return true;
      }

      // Close the overlay
      final result = await FlutterOverlayWindow.closeOverlay();
      if (result != null && result) {
        _isOverlayActive = false;
        _notifyOverlayStatusChange(false);
        logger.i('Overlay stopped successfully');
        return true;
      } else {
        logger.e('Failed to stop overlay');
        return false;
      }
    } catch (e) {
      logger.e('Error stopping overlay: $e');
      return false;
    }
  }

  /// Toggle overlay on/off
  Future<bool> toggleOverlay() async {
    if (_isOverlayActive) {
      return await stopOverlay();
    } else {
      return await startOverlay();
    }
  }

  /// Update overlay size (for expanded/collapsed states)
  Future<bool> updateOverlaySize({
    required int width,
    required int height,
  }) async {
    try {
      if (!_isOverlayActive) {
        logger.w('Cannot update overlay size: overlay not active');
        return false;
      }

      // Note: flutter_overlay_window doesn't support runtime size updates
      // This would require stopping and restarting the overlay
      logger.d('Overlay size update requested: ${width}x$height');

      // For now, we'll log the request
      // In a full implementation, you might need to restart the overlay
      return true;
    } catch (e) {
      logger.e('Error updating overlay size: $e');
      return false;
    }
  }

  /// Update overlay position
  Future<bool> updateOverlayPosition({
    required OverlayAlignment alignment,
  }) async {
    try {
      if (!_isOverlayActive) {
        logger.w('Cannot update overlay position: overlay not active');
        return false;
      }

      // Note: flutter_overlay_window doesn't support runtime position updates
      // This would require stopping and restarting the overlay
      logger.d('Overlay position update requested: $alignment');

      return true;
    } catch (e) {
      logger.e('Error updating overlay position: $e');
      return false;
    }
  }

  /// Check if overlay is supported on this device
  Future<bool> isOverlaySupported() async {
    try {
      // Check Android version and permissions
      return await _permissionService.canShowOverlay();
    } catch (e) {
      logger.e('Error checking overlay support: $e');
      return false;
    }
  }

  /// Get overlay status information
  Future<Map<String, dynamic>> getOverlayStatus() async {
    try {
      return {
        'isActive': _isOverlayActive,
        'hasPermission': await _permissionService.hasOverlayPermission(),
        'isSupported': await isOverlaySupported(),
        'canShow': await _permissionService.canShowOverlay(),
      };
    } catch (e) {
      logger.e('Error getting overlay status: $e');
      return {
        'isActive': false,
        'hasPermission': false,
        'isSupported': false,
        'canShow': false,
        'error': e.toString(),
      };
    }
  }

  /// Send data to overlay window
  Future<bool> sendDataToOverlay(Map<String, dynamic> data) async {
    try {
      if (!_isOverlayActive) {
        logger.w('Cannot send data: overlay not active');
        return false;
      }

      // Send data to overlay window
      await FlutterOverlayWindow.shareData(data);
      logger.d('Data sent to overlay: $data');
      return true;
    } catch (e) {
      logger.e('Error sending data to overlay: $e');
      return false;
    }
  }

  /// Listen for data from overlay window
  Stream<dynamic> get overlayDataStream {
    return FlutterOverlayWindow.overlayListener;
  }

  /// Initialize overlay service
  Future<void> initialize() async {
    try {
      logger.i('Initializing overlay service...');

      // Set up overlay data listener
      overlayDataStream.listen(
        (data) {
          logger.d('Received data from overlay: $data');
          _handleOverlayData(data);
        },
        onError: (error) {
          logger.e('Error in overlay data stream: $error');
        },
      );

      logger.i('Overlay service initialized');
    } catch (e) {
      logger.e('Error initializing overlay service: $e');
    }
  }

  /// Handle data received from overlay
  void _handleOverlayData(dynamic data) {
    try {
      if (data is Map<String, dynamic>) {
        final action = data['action'] as String?;

        switch (action) {
          case 'expand':
            _handleOverlayExpand();
            break;
          case 'collapse':
            _handleOverlayCollapse();
            break;
          case 'close':
            stopOverlay();
            break;
          default:
            logger.d('Unknown overlay action: $action');
        }
      }
    } catch (e) {
      logger.e('Error handling overlay data: $e');
    }
  }

  /// Handle overlay expand action
  void _handleOverlayExpand() {
    // Overlay expand requested
    // Update overlay to expanded size
    updateOverlaySize(
      width: AppConstants.overlayExpandedWidth.toInt(),
      height: AppConstants.overlayExpandedHeight.toInt(),
    );
  }

  /// Handle overlay collapse action
  void _handleOverlayCollapse() {
    logger.d('Overlay collapse requested');
    // Update overlay to collapsed size
    updateOverlaySize(
      width: AppConstants.overlayIconSize.toInt(),
      height: AppConstants.overlayIconSize.toInt(),
    );
  }

  /// Notify listeners about overlay status change
  void _notifyOverlayStatusChange(bool isActive) {
    if (_overlayStatusController != null) {
      _overlayStatusController!.add(isActive);
    }
  }

  /// Dispose resources
  void dispose() {
    _overlayStatusController?.close();
    _overlayStatusController = null;
  }
}
