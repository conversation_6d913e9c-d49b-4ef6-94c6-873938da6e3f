import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'brawler.dart';

part 'draft_state.g.dart';

/// Represents the current state of a draft session
@JsonSerializable()
class DraftState extends Equatable {
    const DraftState({
        required this.currentPhase,
        required this.currentTeam,
        required this.currentPlayer,
        required this.mapName,
        this.teamABans = const [],
        this.teamBBans = const [],
        this.teamAPicks = const [],
        this.teamBPicks = const [],
        this.availableBrawlers = const [],
        this.isPlayerTeamA = true,
        this.timeRemaining = 0,
    });

    /// Current phase of the draft
    final DraftPhase currentPhase;
    
    /// Which team's turn it is
    final Team currentTeam;
    
    /// Which player number is currently picking/banning (1-3)
    final int currentPlayer;
    
    /// Name of the map being played
    final String mapName;
    
    /// Brawlers banned by Team A
    final List<Brawler> teamABans;
    
    /// Brawlers banned by Team B
    final List<Brawler> teamBBans;
    
    /// Brawlers picked by Team A
    final List<Brawler> teamAPicks;
    
    /// Brawlers picked by Team B
    final List<Brawler> teamBPicks;
    
    /// List of brawlers still available for selection
    final List<Brawler> availableBrawlers;
    
    /// Whether the player is on Team A (true) or Team B (false)
    final bool isPlayerTeamA;
    
    /// Time remaining for current pick/ban in seconds
    final int timeRemaining;

    factory DraftState.fromJson(Map<String, dynamic> json) => _$DraftStateFromJson(json);
    Map<String, dynamic> toJson() => _$DraftStateToJson(this);

    /// Creates an initial draft state
    factory DraftState.initial({
        required String mapName,
        required List<Brawler> allBrawlers,
        bool isPlayerTeamA = true,
    }) {
        return DraftState(
            currentPhase: DraftPhase.banPhase,
            currentTeam: Team.teamA,
            currentPlayer: 1,
            mapName: mapName,
            availableBrawlers: allBrawlers,
            isPlayerTeamA: isPlayerTeamA,
            timeRemaining: 30, // 30 seconds for ban phase
        );
    }

    /// Get all banned brawlers
    List<Brawler> get allBans => [...teamABans, ...teamBBans];
    
    /// Get all picked brawlers
    List<Brawler> get allPicks => [...teamAPicks, ...teamBPicks];
    
    /// Get brawlers that are unavailable (banned or picked)
    List<Brawler> get unavailableBrawlers => [...allBans, ...allPicks];
    
    /// Check if draft is complete
    bool get isDraftComplete => 
        teamAPicks.length == 3 && teamBPicks.length == 3;
    
    /// Check if it's the player's turn
    bool get isPlayerTurn => 
        (isPlayerTeamA && currentTeam == Team.teamA) ||
        (!isPlayerTeamA && currentTeam == Team.teamB);

    DraftState copyWith({
        DraftPhase? currentPhase,
        Team? currentTeam,
        int? currentPlayer,
        String? mapName,
        List<Brawler>? teamABans,
        List<Brawler>? teamBBans,
        List<Brawler>? teamAPicks,
        List<Brawler>? teamBPicks,
        List<Brawler>? availableBrawlers,
        bool? isPlayerTeamA,
        int? timeRemaining,
    }) {
        return DraftState(
            currentPhase: currentPhase ?? this.currentPhase,
            currentTeam: currentTeam ?? this.currentTeam,
            currentPlayer: currentPlayer ?? this.currentPlayer,
            mapName: mapName ?? this.mapName,
            teamABans: teamABans ?? this.teamABans,
            teamBBans: teamBBans ?? this.teamBBans,
            teamAPicks: teamAPicks ?? this.teamAPicks,
            teamBPicks: teamBPicks ?? this.teamBPicks,
            availableBrawlers: availableBrawlers ?? this.availableBrawlers,
            isPlayerTeamA: isPlayerTeamA ?? this.isPlayerTeamA,
            timeRemaining: timeRemaining ?? this.timeRemaining,
        );
    }

    @override
    List<Object?> get props => [
        currentPhase,
        currentTeam,
        currentPlayer,
        mapName,
        teamABans,
        teamBBans,
        teamAPicks,
        teamBPicks,
        availableBrawlers,
        isPlayerTeamA,
        timeRemaining,
    ];
}

/// Draft phases
enum DraftPhase {
    @JsonValue('banPhase')
    banPhase,
    @JsonValue('pickPhase')
    pickPhase,
    @JsonValue('completed')
    completed,
}

/// Teams
enum Team {
    @JsonValue('teamA')
    teamA,
    @JsonValue('teamB')
    teamB,
}

/// Extension for draft phase display names
extension DraftPhaseExtension on DraftPhase {
    String get displayName {
        switch (this) {
            case DraftPhase.banPhase:
                return 'Ban Phase';
            case DraftPhase.pickPhase:
                return 'Pick Phase';
            case DraftPhase.completed:
                return 'Draft Complete';
        }
    }
}

/// Extension for team display names
extension TeamExtension on Team {
    String get displayName {
        switch (this) {
            case Team.teamA:
                return 'Team A';
            case Team.teamB:
                return 'Team B';
        }
    }
}
