import 'package:flutter/material.dart';
import 'app_constants.dart';

/// Application theme configuration for Brawl Drafter
class AppTheme {
    // Private constructor to prevent instantiation
    AppTheme._();

    // Color scheme
    static const Color primaryColor = Color(AppConstants.primaryColorValue);
    static const Color accentColor = Color(AppConstants.accentColorValue);
    static const Color backgroundColor = Color(AppConstants.backgroundColorValue);
    static const Color surfaceColor = Color(AppConstants.surfaceColorValue);
    static const Color errorColor = Color(AppConstants.errorColorValue);

    // Additional colors for gaming theme
    static const Color successColor = Color(0xFF4CAF50);
    static const Color warningColor = Color(0xFFFF9800);
    static const Color infoColor = Color(0xFF2196F3);
    
    // Text colors
    static const Color primaryTextColor = Color(0xFFFFFFFF);
    static const Color secondaryTextColor = Color(0xFFB0B0B0);
    static const Color disabledTextColor = Color(0xFF666666);

    // Overlay specific colors
    static const Color overlayBackgroundColor = Color(0xE6000000); // 90% opacity black
    static const Color overlayBorderColor = Color(0xFF333333);
    static const Color overlayAccentColor = primaryColor;

    // Brawler rarity colors
    static const Color commonColor = Color(0xFF9E9E9E);
    static const Color rareColor = Color(0xFF4CAF50);
    static const Color superRareColor = Color(0xFF2196F3);
    static const Color epicColor = Color(0xFF9C27B0);
    static const Color mythicColor = Color(0xFFE91E63);
    static const Color legendaryColor = Color(0xFFFFEB3B);
    static const Color chromaticColor = Color(0xFFFF5722);

    // Main app theme
    static ThemeData get darkTheme {
        return ThemeData(
            useMaterial3: true,
            brightness: Brightness.dark,
            primaryColor: primaryColor,
            scaffoldBackgroundColor: backgroundColor,
            colorScheme: const ColorScheme.dark(
                primary: primaryColor,
                secondary: accentColor,
                surface: surfaceColor,
                background: backgroundColor,
                error: errorColor,
                onPrimary: Colors.white,
                onSecondary: Colors.white,
                onSurface: primaryTextColor,
                onBackground: primaryTextColor,
                onError: Colors.white,
            ),
            appBarTheme: const AppBarTheme(
                backgroundColor: surfaceColor,
                foregroundColor: primaryTextColor,
                elevation: 4,
                centerTitle: true,
            ),
            cardTheme: CardTheme(
                color: surfaceColor,
                elevation: 4,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                    ),
                ),
            ),
            textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                    foregroundColor: primaryColor,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                    ),
                ),
            ),
            outlinedButtonTheme: OutlinedButtonThemeData(
                style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: const BorderSide(color: primaryColor),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                    ),
                ),
            ),
            inputDecorationTheme: InputDecorationTheme(
                filled: true,
                fillColor: surfaceColor,
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.grey),
                ),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: primaryColor, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: errorColor),
                ),
                labelStyle: const TextStyle(color: secondaryTextColor),
                hintStyle: const TextStyle(color: disabledTextColor),
            ),
            switchTheme: SwitchThemeData(
                thumbColor: MaterialStateProperty.resolveWith((states) {
                    if (states.contains(MaterialState.selected)) {
                        return primaryColor;
                    }
                    return Colors.grey;
                }),
                trackColor: MaterialStateProperty.resolveWith((states) {
                    if (states.contains(MaterialState.selected)) {
                        return primaryColor.withOpacity(0.5);
                    }
                    return Colors.grey.withOpacity(0.3);
                }),
            ),
            sliderTheme: const SliderThemeData(
                activeTrackColor: primaryColor,
                inactiveTrackColor: Colors.grey,
                thumbColor: primaryColor,
                overlayColor: Color(0x1F1976D2),
            ),
            progressIndicatorTheme: const ProgressIndicatorThemeData(
                color: primaryColor,
                linearTrackColor: Colors.grey,
                circularTrackColor: Colors.grey,
            ),
            dividerTheme: const DividerThemeData(
                color: Colors.grey,
                thickness: 1,
            ),
            textTheme: const TextTheme(
                displayLarge: TextStyle(
                    color: primaryTextColor,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                ),
                displayMedium: TextStyle(
                    color: primaryTextColor,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                ),
                displaySmall: TextStyle(
                    color: primaryTextColor,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                ),
                headlineLarge: TextStyle(
                    color: primaryTextColor,
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                ),
                headlineMedium: TextStyle(
                    color: primaryTextColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                ),
                headlineSmall: TextStyle(
                    color: primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                ),
                titleLarge: TextStyle(
                    color: primaryTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                ),
                titleMedium: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                ),
                titleSmall: TextStyle(
                    color: primaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                ),
                bodyLarge: TextStyle(
                    color: primaryTextColor,
                    fontSize: 16,
                ),
                bodyMedium: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                ),
                bodySmall: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 12,
                ),
                labelLarge: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                ),
                labelMedium: TextStyle(
                    color: primaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                ),
                labelSmall: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                ),
            ),
        );
    }

    // Overlay-specific theme for floating widgets
    static ThemeData get overlayTheme {
        return darkTheme.copyWith(
            scaffoldBackgroundColor: Colors.transparent,
            cardTheme: CardTheme(
                color: overlayBackgroundColor,
                elevation: 8,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.overlayCornerRadius),
                    side: const BorderSide(
                        color: overlayBorderColor,
                        width: 1,
                    ),
                ),
            ),
        );
    }

    // Get color for brawler rarity
    static Color getRarityColor(String rarity) {
        switch (rarity.toLowerCase()) {
            case 'common':
                return commonColor;
            case 'rare':
                return rareColor;
            case 'superrare':
            case 'super rare':
                return superRareColor;
            case 'epic':
                return epicColor;
            case 'mythic':
                return mythicColor;
            case 'legendary':
                return legendaryColor;
            case 'chromatic':
                return chromaticColor;
            default:
                return commonColor;
        }
    }

    // Get status color
    static Color getStatusColor(String status) {
        switch (status.toLowerCase()) {
            case 'success':
            case 'available':
            case 'online':
                return successColor;
            case 'warning':
            case 'limited':
                return warningColor;
            case 'error':
            case 'unavailable':
            case 'banned':
                return errorColor;
            case 'info':
            case 'picked':
                return infoColor;
            default:
                return primaryColor;
        }
    }
}
