// ...existing code...
import 'package:hive_flutter/hive_flutter.dart';

import '../../main.dart';
import '../constants/app_constants.dart';
import 'permission_service.dart';
import 'overlay_service.dart';
import 'background_service.dart';

/// Main application service that coordinates all other services
class AppService {
  static final AppService _instance = AppService._internal();
  factory AppService() => _instance;
  AppService._internal();

  final PermissionService _permissionService = PermissionService();
  final OverlayService _overlayService = OverlayService();
  final BackgroundService _backgroundService = BackgroundService();

  bool _isInitialized = false;
  Box? _settingsBox;
  Box? _dataBox;

  /// Check if app is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize the application
  Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        logger.w('App is already initialized');
        return true;
      }

      logger.i('Initializing Brawl Drafter app...');

      // Initialize Hive boxes
      await _initializeStorage();

      // Initialize services
      await _initializeServices();

      // Load app settings
      await _loadSettings();

      // Check permissions
      await _checkInitialPermissions();

      _isInitialized = true;
      logger.i('App initialization completed successfully');
      return true;
    } catch (e) {
      logger.e('Error during app initialization: $e');
      return false;
    }
  }

  /// Initialize local storage
  Future<void> _initializeStorage() async {
    try {
      logger.d('Initializing local storage...');

      // Open Hive boxes
      _settingsBox = await Hive.openBox('settings');
      _dataBox = await Hive.openBox('data');

      logger.d('Local storage initialized');
    } catch (e) {
      logger.e('Error initializing storage: $e');
      rethrow;
    }
  }

  /// Initialize all services
  Future<void> _initializeServices() async {
    try {
      logger.d('Initializing services...');

      // Initialize overlay service
      await _overlayService.initialize();

      logger.d('Services initialized');
    } catch (e) {
      logger.e('Error initializing services: $e');
      rethrow;
    }
  }

  /// Load app settings from storage
  Future<void> _loadSettings() async {
    try {
      logger.d('Loading app settings...');

      // Load settings with defaults
      final overlayPosition =
          _settingsBox?.get(
                AppConstants.overlayPositionKey,
                defaultValue: 'topRight',
              )
              as String?;

      logger.d('Settings loaded - overlay position: $overlayPosition');
    } catch (e) {
      logger.e('Error loading settings: $e');
      // Continue with defaults
    }
  }

  /// Check initial permissions status
  Future<void> _checkInitialPermissions() async {
    try {
      logger.d('Checking initial permissions...');

      final hasOverlay = await _permissionService.hasOverlayPermission();
      final hasAll = await _permissionService.hasAllPermissions();

      logger.d('Permissions status - overlay: $hasOverlay, all: $hasAll');
    } catch (e) {
      logger.e('Error checking permissions: $e');
      // Continue without permissions
    }
  }

  /// Start all app services
  Future<bool> startServices() async {
    try {
      if (!_isInitialized) {
        logger.w('Cannot start services: app not initialized');
        return false;
      }

      logger.i('Starting app services...');

      // Check permissions first
      final hasPermissions = await _permissionService.hasAllPermissions();
      if (!hasPermissions) {
        logger.w('Cannot start services: permissions not granted');
        return false;
      }

      // Start background processing
      final backgroundStarted = await _backgroundService
          .startBackgroundProcessing();
      if (!backgroundStarted) {
        logger.e('Failed to start background service');
        return false;
      }

      // Start overlay service
      final overlayStarted = await _overlayService.startOverlay();
      if (!overlayStarted) {
        logger.e('Failed to start overlay service');
        // Stop background service if overlay failed
        await _backgroundService.stopBackgroundProcessing();
        return false;
      }

      logger.i('All services started successfully');
      return true;
    } catch (e) {
      logger.e('Error starting services: $e');
      return false;
    }
  }

  /// Stop all app services
  Future<bool> stopServices() async {
    try {
      logger.i('Stopping app services...');

      // Stop overlay service
      await _overlayService.stopOverlay();

      // Stop background processing
      await _backgroundService.stopBackgroundProcessing();

      logger.i('All services stopped');
      return true;
    } catch (e) {
      logger.e('Error stopping services: $e');
      return false;
    }
  }

  /// Request all required permissions
  Future<bool> requestPermissions() async {
    try {
      logger.i('Requesting app permissions...');
      return await _permissionService.requestAllPermissions();
    } catch (e) {
      logger.e('Error requesting permissions: $e');
      return false;
    }
  }

  /// Get app status information
  Future<Map<String, dynamic>> getAppStatus() async {
    try {
      final permissionStatuses = await _permissionService
          .getAllPermissionStatuses();
      final overlayStatus = await _overlayService.getOverlayStatus();
      final backgroundStatus = _backgroundService.getStatus();

      return {
        'isInitialized': _isInitialized,
        'permissions': permissionStatuses,
        'overlay': overlayStatus,
        'background': backgroundStatus,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      logger.e('Error getting app status: $e');
      return {
        'isInitialized': false,
        'error': e.toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }

  /// Save app setting
  Future<void> saveSetting(String key, dynamic value) async {
    try {
      await _settingsBox?.put(key, value);
      logger.d('Setting saved: $key = $value');
    } catch (e) {
      logger.e('Error saving setting $key: $e');
    }
  }

  /// Get app setting
  T? getSetting<T>(String key, {T? defaultValue}) {
    try {
      return _settingsBox?.get(key, defaultValue: defaultValue) as T?;
    } catch (e) {
      logger.e('Error getting setting $key: $e');
      return defaultValue;
    }
  }

  /// Save app data
  Future<void> saveData(String key, dynamic value) async {
    try {
      await _dataBox?.put(key, value);
      logger.d('Data saved: $key');
    } catch (e) {
      logger.e('Error saving data $key: $e');
    }
  }

  /// Get app data
  T? getData<T>(String key, {T? defaultValue}) {
    try {
      return _dataBox?.get(key, defaultValue: defaultValue) as T?;
    } catch (e) {
      logger.e('Error getting data $key: $e');
      return defaultValue;
    }
  }

  /// Clear all app data
  Future<void> clearAllData() async {
    try {
      logger.i('Clearing all app data...');

      await _settingsBox?.clear();
      await _dataBox?.clear();

      logger.i('All app data cleared');
    } catch (e) {
      logger.e('Error clearing app data: $e');
    }
  }

  /// Reset app to initial state
  Future<void> resetApp() async {
    try {
      logger.i('Resetting app to initial state...');

      // Stop all services
      await stopServices();

      // Clear all data
      await clearAllData();

      // Reset initialization flag
      _isInitialized = false;

      logger.i('App reset completed');
    } catch (e) {
      logger.e('Error resetting app: $e');
    }
  }

  /// Get service instances
  PermissionService get permissionService => _permissionService;
  OverlayService get overlayService => _overlayService;
  BackgroundService get backgroundService => _backgroundService;

  /// Dispose all resources
  Future<void> dispose() async {
    try {
      logger.i('Disposing app service...');

      // Stop all services
      await stopServices();

      // Dispose services
      _overlayService.dispose();
      _backgroundService.dispose();

      // Close storage boxes
      await _settingsBox?.close();
      await _dataBox?.close();

      _isInitialized = false;
      logger.i('App service disposed');
    } catch (e) {
      logger.e('Error disposing app service: $e');
    }
  }
}
