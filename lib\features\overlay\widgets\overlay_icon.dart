import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/constants/app_theme.dart';

/// Collapsed overlay icon widget
/// Shows a small draggable icon when overlay is minimized
class OverlayIcon extends StatefulWidget {
    const OverlayIcon({
        super.key,
        required this.onTap,
        required this.onLongPress,
    });

    final VoidCallback onTap;
    final VoidCallback onLongPress;

    @override
    State<OverlayIcon> createState() => _OverlayIconState();
}

class _OverlayIconState extends State<OverlayIcon>
    with SingleTickerProviderStateMixin {
    late AnimationController _pulseController;
    late Animation<double> _pulseAnimation;
    bool _isPressed = false;

    @override
    void initState() {
        super.initState();
        
        // Initialize pulse animation
        _pulseController = AnimationController(
            duration: const Duration(milliseconds: 1500),
            vsync: this,
        );
        
        _pulseAnimation = Tween<double>(
            begin: 1.0,
            end: 1.1,
        ).animate(CurvedAnimation(
            parent: _pulseController,
            curve: Curves.easeInOut,
        ));

        // Start subtle pulse animation
        _startPulseAnimation();
    }

    @override
    void dispose() {
        _pulseController.dispose();
        super.dispose();
    }

    /// Start the pulse animation loop
    void _startPulseAnimation() {
        _pulseController.repeat(reverse: true);
    }

    /// Stop the pulse animation
    void _stopPulseAnimation() {
        _pulseController.stop();
        _pulseController.reset();
    }

    @override
    Widget build(BuildContext context) {
        return Center(
            child: GestureDetector(
                onTap: widget.onTap,
                onLongPress: widget.onLongPress,
                onTapDown: (_) {
                    setState(() {
                        _isPressed = true;
                    });
                    _stopPulseAnimation();
                },
                onTapUp: (_) {
                    setState(() {
                        _isPressed = false;
                    });
                    _startPulseAnimation();
                },
                onTapCancel: () {
                    setState(() {
                        _isPressed = false;
                    });
                    _startPulseAnimation();
                },
                child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                        return Transform.scale(
                            scale: _isPressed 
                                ? 0.95 
                                : _pulseAnimation.value,
                            child: Container(
                                width: AppConstants.overlayIconSize,
                                height: AppConstants.overlayIconSize,
                                decoration: BoxDecoration(
                                    color: AppTheme.overlayBackgroundColor,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: AppTheme.primaryColor,
                                        width: 2,
                                    ),
                                    boxShadow: [
                                        BoxShadow(
                                            color: AppTheme.primaryColor.withValues(alpha: 0.3),
                                            blurRadius: 8,
                                            spreadRadius: 2,
                                        ),
                                        BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.2),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                        ),
                                    ],
                                ),
                                child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                        // Main icon
                                        Icon(
                                            MdiIcons.swordCross,
                                            color: AppTheme.primaryColor,
                                            size: 28,
                                        ),
                                        
                                        // Status indicator
                                        Positioned(
                                            top: 8,
                                            right: 8,
                                            child: Container(
                                                width: 12,
                                                height: 12,
                                                decoration: BoxDecoration(
                                                    color: AppTheme.successColor,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                        color: AppTheme.overlayBackgroundColor,
                                                        width: 1,
                                                    ),
                                                ),
                                            ),
                                        ),
                                    ],
                                ),
                            ),
                        );
                    },
                ),
            ),
        );
    }
}

/// Overlay icon with notification badge
class OverlayIconWithBadge extends StatelessWidget {
    const OverlayIconWithBadge({
        super.key,
        required this.onTap,
        required this.onLongPress,
        this.badgeCount = 0,
        this.showBadge = false,
    });

    final VoidCallback onTap;
    final VoidCallback onLongPress;
    final int badgeCount;
    final bool showBadge;

    @override
    Widget build(BuildContext context) {
        return Stack(
            clipBehavior: Clip.none,
            children: [
                OverlayIcon(
                    onTap: onTap,
                    onLongPress: onLongPress,
                ),
                
                // Notification badge
                if (showBadge && badgeCount > 0)
                    Positioned(
                        top: -4,
                        right: -4,
                        child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                                color: AppTheme.errorColor,
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: AppTheme.overlayBackgroundColor,
                                    width: 1,
                                ),
                            ),
                            constraints: const BoxConstraints(
                                minWidth: 20,
                                minHeight: 20,
                            ),
                            child: Text(
                                badgeCount > 99 ? '99+' : badgeCount.toString(),
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                            ),
                        ),
                    ),
            ],
        );
    }
}

/// Overlay icon states
enum OverlayIconState {
    idle,
    active,
    warning,
    error,
}

/// Overlay icon with different states
class StatefulOverlayIcon extends StatelessWidget {
    const StatefulOverlayIcon({
        super.key,
        required this.onTap,
        required this.onLongPress,
        this.state = OverlayIconState.idle,
    });

    final VoidCallback onTap;
    final VoidCallback onLongPress;
    final OverlayIconState state;

    Color get _stateColor {
        switch (state) {
            case OverlayIconState.idle:
                return AppTheme.primaryColor;
            case OverlayIconState.active:
                return AppTheme.successColor;
            case OverlayIconState.warning:
                return AppTheme.warningColor;
            case OverlayIconState.error:
                return AppTheme.errorColor;
        }
    }

    IconData get _stateIcon {
        switch (state) {
            case OverlayIconState.idle:
                return MdiIcons.swordCross;
            case OverlayIconState.active:
                return MdiIcons.checkCircle;
            case OverlayIconState.warning:
                return MdiIcons.alertCircle;
            case OverlayIconState.error:
                return MdiIcons.closeCircle;
        }
    }

    @override
    Widget build(BuildContext context) {
        return Center(
            child: GestureDetector(
                onTap: onTap,
                onLongPress: onLongPress,
                child: Container(
                    width: AppConstants.overlayIconSize,
                    height: AppConstants.overlayIconSize,
                    decoration: BoxDecoration(
                        color: AppTheme.overlayBackgroundColor,
                        shape: BoxShape.circle,
                        border: Border.all(
                            color: _stateColor,
                            width: 2,
                        ),
                        boxShadow: [
                            BoxShadow(
                                color: _stateColor.withValues(alpha: 0.3),
                                blurRadius: 8,
                                spreadRadius: 2,
                            ),
                        ],
                    ),
                    child: Icon(
                        _stateIcon,
                        color: _stateColor,
                        size: 28,
                    ),
                ),
            ),
        );
    }
}
