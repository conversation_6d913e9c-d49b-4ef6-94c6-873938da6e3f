import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'brawler.g.dart';

/// Represents a Brawl Stars brawler with all relevant information
@JsonSerializable()
class Brawler extends Equatable {
    const Brawler({
        required this.id,
        required this.name,
        required this.rarity,
        required this.type,
        required this.iconPath,
        this.winRate = 0.0,
        this.pickRate = 0.0,
        this.banRate = 0.0,
        this.isUnlocked = true,
    });

    /// Unique identifier for the brawler
    final String id;
    
    /// Display name of the brawler
    final String name;
    
    /// Rarity level (Common, Rare, Super Rare, Epic, Mythic, Legendary, Chromatic)
    final BrawlerRarity rarity;
    
    /// Brawler type/role (Tank, Damage Dealer, Support, etc.)
    final BrawlerType type;
    
    /// Path to the brawler's icon asset
    final String iconPath;
    
    /// Current win rate percentage (0.0 to 100.0)
    final double winRate;
    
    /// Current pick rate percentage (0.0 to 100.0)
    final double pickRate;
    
    /// Current ban rate percentage (0.0 to 100.0)
    final double banRate;
    
    /// Whether the player has unlocked this brawler
    final bool isUnlocked;

    factory Brawler.fromJson(Map<String, dynamic> json) => _$BrawlerFromJson(json);
    Map<String, dynamic> toJson() => _$BrawlerToJson(this);

    Brawler copyWith({
        String? id,
        String? name,
        BrawlerRarity? rarity,
        BrawlerType? type,
        String? iconPath,
        double? winRate,
        double? pickRate,
        double? banRate,
        bool? isUnlocked,
    }) {
        return Brawler(
            id: id ?? this.id,
            name: name ?? this.name,
            rarity: rarity ?? this.rarity,
            type: type ?? this.type,
            iconPath: iconPath ?? this.iconPath,
            winRate: winRate ?? this.winRate,
            pickRate: pickRate ?? this.pickRate,
            banRate: banRate ?? this.banRate,
            isUnlocked: isUnlocked ?? this.isUnlocked,
        );
    }

    @override
    List<Object?> get props => [
        id,
        name,
        rarity,
        type,
        iconPath,
        winRate,
        pickRate,
        banRate,
        isUnlocked,
    ];
}

/// Brawler rarity levels
enum BrawlerRarity {
    @JsonValue('common')
    common,
    @JsonValue('rare')
    rare,
    @JsonValue('superRare')
    superRare,
    @JsonValue('epic')
    epic,
    @JsonValue('mythic')
    mythic,
    @JsonValue('legendary')
    legendary,
    @JsonValue('chromatic')
    chromatic,
}

/// Brawler types/roles
enum BrawlerType {
    @JsonValue('tank')
    tank,
    @JsonValue('fighter')
    fighter,
    @JsonValue('assassin')
    assassin,
    @JsonValue('damageDealer')
    damageDealer,
    @JsonValue('marksman')
    marksman,
    @JsonValue('thrower')
    thrower,
    @JsonValue('support')
    support,
    @JsonValue('controller')
    controller,
}

/// Extension to get display names for brawler rarities
extension BrawlerRarityExtension on BrawlerRarity {
    String get displayName {
        switch (this) {
            case BrawlerRarity.common:
                return 'Common';
            case BrawlerRarity.rare:
                return 'Rare';
            case BrawlerRarity.superRare:
                return 'Super Rare';
            case BrawlerRarity.epic:
                return 'Epic';
            case BrawlerRarity.mythic:
                return 'Mythic';
            case BrawlerRarity.legendary:
                return 'Legendary';
            case BrawlerRarity.chromatic:
                return 'Chromatic';
        }
    }
}

/// Extension to get display names for brawler types
extension BrawlerTypeExtension on BrawlerType {
    String get displayName {
        switch (this) {
            case BrawlerType.tank:
                return 'Tank';
            case BrawlerType.fighter:
                return 'Fighter';
            case BrawlerType.assassin:
                return 'Assassin';
            case BrawlerType.damageDealer:
                return 'Damage Dealer';
            case BrawlerType.marksman:
                return 'Marksman';
            case BrawlerType.thrower:
                return 'Thrower';
            case BrawlerType.support:
                return 'Support';
            case BrawlerType.controller:
                return 'Controller';
        }
    }
}
