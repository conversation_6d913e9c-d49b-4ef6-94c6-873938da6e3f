import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../core/constants/app_theme.dart';
import '../../shared/providers/app_providers.dart';

/// Simple test overlay for verifying overlay functionality
/// This can be used to test the overlay system before implementing the full UI
void testOverlayMain() {
    runApp(
        const ProviderScope(
            child: TestOverlayApp(),
        ),
    );
}

class TestOverlayApp extends StatelessWidget {
    const TestOverlayApp({super.key});

    @override
    Widget build(BuildContext context) {
        return MaterialApp(
            title: 'Test Overlay',
            theme: AppTheme.overlayTheme,
            debugShowCheckedModeBanner: false,
            home: const TestOverlayWidget(),
        );
    }
}

class TestOverlayWidget extends ConsumerStatefulWidget {
    const TestOverlayWidget({super.key});

    @override
    ConsumerState<TestOverlayWidget> createState() => _TestOverlayWidgetState();
}

class _TestOverlayWidgetState extends ConsumerState<TestOverlayWidget>
    with SingleTickerProviderStateMixin {
    bool _isExpanded = false;
    late AnimationController _animationController;
    late Animation<double> _scaleAnimation;

    @override
    void initState() {
        super.initState();
        
        _animationController = AnimationController(
            duration: const Duration(milliseconds: 300),
            vsync: this,
        );
        
        _scaleAnimation = Tween<double>(
            begin: 1.0,
            end: 3.0,
        ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.elasticOut,
        ));
    }

    @override
    void dispose() {
        _animationController.dispose();
        super.dispose();
    }

    void _toggleExpanded() {
        setState(() {
            _isExpanded = !_isExpanded;
        });
        
        if (_isExpanded) {
            _animationController.forward();
        } else {
            _animationController.reverse();
        }
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: Colors.transparent,
            body: Center(
                child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                        return Transform.scale(
                            scale: _scaleAnimation.value,
                            child: GestureDetector(
                                onTap: _toggleExpanded,
                                onLongPress: () {
                                    // Close overlay
                                    Navigator.of(context).pop();
                                },
                                child: Container(
                                    width: 60,
                                    height: 60,
                                    decoration: BoxDecoration(
                                        color: AppTheme.overlayBackgroundColor,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                            color: AppTheme.primaryColor,
                                            width: 2,
                                        ),
                                        boxShadow: [
                                            BoxShadow(
                                                color: AppTheme.primaryColor.withValues(alpha: 0.3),
                                                blurRadius: 8,
                                                spreadRadius: 2,
                                            ),
                                        ],
                                    ),
                                    child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                            Icon(
                                                _isExpanded 
                                                    ? MdiIcons.close 
                                                    : MdiIcons.swordCross,
                                                color: AppTheme.primaryColor,
                                                size: 28,
                                            ),
                                            
                                            if (_isExpanded)
                                                Positioned(
                                                    bottom: -40,
                                                    child: Container(
                                                        padding: const EdgeInsets.symmetric(
                                                            horizontal: 8,
                                                            vertical: 4,
                                                        ),
                                                        decoration: BoxDecoration(
                                                            color: AppTheme.overlayBackgroundColor,
                                                            borderRadius: BorderRadius.circular(4),
                                                            border: Border.all(
                                                                color: AppTheme.primaryColor,
                                                                width: 1,
                                                            ),
                                                        ),
                                                        child: const Text(
                                                            'Test Overlay\nWorking!',
                                                            style: TextStyle(
                                                                color: AppTheme.primaryTextColor,
                                                                fontSize: 10,
                                                            ),
                                                            textAlign: TextAlign.center,
                                                        ),
                                                    ),
                                                ),
                                        ],
                                    ),
                                ),
                            ),
                        );
                    },
                ),
            ),
        );
    }
}

/// Advanced test overlay with more features
class AdvancedTestOverlay extends ConsumerStatefulWidget {
    const AdvancedTestOverlay({super.key});

    @override
    ConsumerState<AdvancedTestOverlay> createState() => _AdvancedTestOverlayState();
}

class _AdvancedTestOverlayState extends ConsumerState<AdvancedTestOverlay> {
    int _tapCount = 0;
    DateTime _lastTap = DateTime.now();

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: Colors.transparent,
            body: Center(
                child: GestureDetector(
                    onTap: () {
                        setState(() {
                            _tapCount++;
                            _lastTap = DateTime.now();
                        });
                    },
                    onLongPress: () {
                        // Reset counter
                        setState(() {
                            _tapCount = 0;
                        });
                    },
                    child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                            color: AppTheme.overlayBackgroundColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                                color: AppTheme.primaryColor,
                                width: 2,
                            ),
                            boxShadow: [
                                BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                ),
                            ],
                        ),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                                Icon(
                                    MdiIcons.testTube,
                                    color: AppTheme.primaryColor,
                                    size: 24,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                    'Taps: $_tapCount',
                                    style: const TextStyle(
                                        color: AppTheme.primaryTextColor,
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                    ),
                                ),
                                Text(
                                    _formatTime(_lastTap),
                                    style: const TextStyle(
                                        color: AppTheme.secondaryTextColor,
                                        fontSize: 8,
                                    ),
                                ),
                            ],
                        ),
                    ),
                ),
            ),
        );
    }

    String _formatTime(DateTime time) {
        return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:${time.second.toString().padLeft(2, '0')}';
    }
}
