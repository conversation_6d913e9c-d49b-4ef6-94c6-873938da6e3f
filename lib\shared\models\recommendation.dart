import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'brawler.dart';

part 'recommendation.g.dart';

/// Represents a brawler recommendation with reasoning
@JsonSerializable()
class Recommendation extends Equatable {
    const Recommendation({
        required this.brawler,
        required this.score,
        required this.reasons,
        required this.type,
        this.priority = RecommendationPriority.medium,
    });

    /// The recommended brawler
    final Brawler brawler;
    
    /// Recommendation score (0.0 to 100.0)
    final double score;
    
    /// List of reasons for this recommendation
    final List<String> reasons;
    
    /// Type of recommendation (pick or ban)
    final RecommendationType type;
    
    /// Priority level of this recommendation
    final RecommendationPriority priority;

    factory Recommendation.fromJson(Map<String, dynamic> json) => 
        _$RecommendationFromJson(json);
    Map<String, dynamic> toJson() => _$RecommendationToJson(this);

    Recommendation copyWith({
        Brawler? brawler,
        double? score,
        List<String>? reasons,
        RecommendationType? type,
        RecommendationPriority? priority,
    }) {
        return Recommendation(
            brawler: brawler ?? this.brawler,
            score: score ?? this.score,
            reasons: reasons ?? this.reasons,
            type: type ?? this.type,
            priority: priority ?? this.priority,
        );
    }

    @override
    List<Object?> get props => [brawler, score, reasons, type, priority];
}

/// Type of recommendation
enum RecommendationType {
    @JsonValue('pick')
    pick,
    @JsonValue('ban')
    ban,
}

/// Priority level for recommendations
enum RecommendationPriority {
    @JsonValue('low')
    low,
    @JsonValue('medium')
    medium,
    @JsonValue('high')
    high,
    @JsonValue('critical')
    critical,
}

/// Represents a set of recommendations for the current draft state
@JsonSerializable()
class RecommendationSet extends Equatable {
    const RecommendationSet({
        required this.picks,
        required this.bans,
        required this.generatedAt,
        this.mapSpecificNotes = const [],
        this.teamCompositionNotes = const [],
    });

    /// Pick recommendations
    final List<Recommendation> picks;
    
    /// Ban recommendations
    final List<Recommendation> bans;
    
    /// When these recommendations were generated
    final DateTime generatedAt;
    
    /// Map-specific strategy notes
    final List<String> mapSpecificNotes;
    
    /// Team composition strategy notes
    final List<String> teamCompositionNotes;

    factory RecommendationSet.fromJson(Map<String, dynamic> json) => 
        _$RecommendationSetFromJson(json);
    Map<String, dynamic> toJson() => _$RecommendationSetToJson(this);

    /// Get top N pick recommendations
    List<Recommendation> getTopPicks(int count) {
        final sortedPicks = List<Recommendation>.from(picks)
            ..sort((a, b) => b.score.compareTo(a.score));
        return sortedPicks.take(count).toList();
    }

    /// Get top N ban recommendations
    List<Recommendation> getTopBans(int count) {
        final sortedBans = List<Recommendation>.from(bans)
            ..sort((a, b) => b.score.compareTo(a.score));
        return sortedBans.take(count).toList();
    }

    /// Get recommendations by priority
    List<Recommendation> getByPriority(RecommendationPriority priority) {
        return [...picks, ...bans]
            .where((rec) => rec.priority == priority)
            .toList();
    }

    RecommendationSet copyWith({
        List<Recommendation>? picks,
        List<Recommendation>? bans,
        DateTime? generatedAt,
        List<String>? mapSpecificNotes,
        List<String>? teamCompositionNotes,
    }) {
        return RecommendationSet(
            picks: picks ?? this.picks,
            bans: bans ?? this.bans,
            generatedAt: generatedAt ?? this.generatedAt,
            mapSpecificNotes: mapSpecificNotes ?? this.mapSpecificNotes,
            teamCompositionNotes: teamCompositionNotes ?? this.teamCompositionNotes,
        );
    }

    @override
    List<Object?> get props => [
        picks,
        bans,
        generatedAt,
        mapSpecificNotes,
        teamCompositionNotes,
    ];
}

/// Extensions for enum display names
extension RecommendationTypeExtension on RecommendationType {
    String get displayName {
        switch (this) {
            case RecommendationType.pick:
                return 'Pick';
            case RecommendationType.ban:
                return 'Ban';
        }
    }
}

extension RecommendationPriorityExtension on RecommendationPriority {
    String get displayName {
        switch (this) {
            case RecommendationPriority.low:
                return 'Low';
            case RecommendationPriority.medium:
                return 'Medium';
            case RecommendationPriority.high:
                return 'High';
            case RecommendationPriority.critical:
                return 'Critical';
        }
    }
}
