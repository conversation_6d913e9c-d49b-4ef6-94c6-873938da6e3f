// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommendation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Recommendation _$RecommendationFromJson(Map<String, dynamic> json) =>
    Recommendation(
      brawler: Brawler.fromJson(json['brawler'] as Map<String, dynamic>),
      score: (json['score'] as num).toDouble(),
      reasons:
          (json['reasons'] as List<dynamic>).map((e) => e as String).toList(),
      type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
      priority: $enumDecodeNullable(
              _$RecommendationPriorityEnumMap, json['priority']) ??
          RecommendationPriority.medium,
    );

Map<String, dynamic> _$RecommendationToJson(Recommendation instance) =>
    <String, dynamic>{
      'brawler': instance.brawler,
      'score': instance.score,
      'reasons': instance.reasons,
      'type': _$RecommendationTypeEnumMap[instance.type]!,
      'priority': _$RecommendationPriorityEnumMap[instance.priority]!,
    };

const _$RecommendationTypeEnumMap = {
  RecommendationType.pick: 'pick',
  RecommendationType.ban: 'ban',
};

const _$RecommendationPriorityEnumMap = {
  RecommendationPriority.low: 'low',
  RecommendationPriority.medium: 'medium',
  RecommendationPriority.high: 'high',
  RecommendationPriority.critical: 'critical',
};

RecommendationSet _$RecommendationSetFromJson(Map<String, dynamic> json) =>
    RecommendationSet(
      picks: (json['picks'] as List<dynamic>)
          .map((e) => Recommendation.fromJson(e as Map<String, dynamic>))
          .toList(),
      bans: (json['bans'] as List<dynamic>)
          .map((e) => Recommendation.fromJson(e as Map<String, dynamic>))
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      mapSpecificNotes: (json['mapSpecificNotes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      teamCompositionNotes: (json['teamCompositionNotes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$RecommendationSetToJson(RecommendationSet instance) =>
    <String, dynamic>{
      'picks': instance.picks,
      'bans': instance.bans,
      'generatedAt': instance.generatedAt.toIso8601String(),
      'mapSpecificNotes': instance.mapSpecificNotes,
      'teamCompositionNotes': instance.teamCompositionNotes,
    };
