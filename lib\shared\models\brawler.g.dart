// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'brawler.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Brawler _$BrawlerFromJson(Map<String, dynamic> json) => Brawler(
      id: json['id'] as String,
      name: json['name'] as String,
      rarity: $enumDecode(_$BrawlerRarityEnumMap, json['rarity']),
      type: $enumDecode(_$BrawlerTypeEnumMap, json['type']),
      iconPath: json['iconPath'] as String,
      winRate: (json['winRate'] as num?)?.toDouble() ?? 0.0,
      pickRate: (json['pickRate'] as num?)?.toDouble() ?? 0.0,
      banRate: (json['banRate'] as num?)?.toDouble() ?? 0.0,
      isUnlocked: json['isUnlocked'] as bool? ?? true,
    );

Map<String, dynamic> _$BrawlerToJson(Brawler instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'rarity': _$BrawlerRarityEnumMap[instance.rarity]!,
      'type': _$BrawlerTypeEnumMap[instance.type]!,
      'iconPath': instance.iconPath,
      'winRate': instance.winRate,
      'pickRate': instance.pickRate,
      'banRate': instance.banRate,
      'isUnlocked': instance.isUnlocked,
    };

const _$BrawlerRarityEnumMap = {
  BrawlerRarity.common: 'common',
  BrawlerRarity.rare: 'rare',
  BrawlerRarity.superRare: 'superRare',
  BrawlerRarity.epic: 'epic',
  BrawlerRarity.mythic: 'mythic',
  BrawlerRarity.legendary: 'legendary',
  BrawlerRarity.chromatic: 'chromatic',
};

const _$BrawlerTypeEnumMap = {
  BrawlerType.tank: 'tank',
  BrawlerType.fighter: 'fighter',
  BrawlerType.assassin: 'assassin',
  BrawlerType.damageDealer: 'damageDealer',
  BrawlerType.marksman: 'marksman',
  BrawlerType.thrower: 'thrower',
  BrawlerType.support: 'support',
  BrawlerType.controller: 'controller',
};
