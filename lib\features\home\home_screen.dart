import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../core/constants/app_constants.dart';
import '../../core/constants/app_theme.dart';
import '../../shared/providers/app_providers.dart';

/// Main home screen for the Brawl Drafter app
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardTab(),
    const SettingsTab(),
    const AboutTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(MdiIcons.help),
            onPressed: () {
              _showHelpDialog(context);
            },
          ),
        ],
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppTheme.surfaceColor,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: AppTheme.secondaryTextColor,
        items: [
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.viewDashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(icon: Icon(MdiIcons.cog), label: 'Settings'),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.information),
            label: 'About',
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('How to Use Brawl Drafter'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '1. Grant overlay permission to show floating widget over Brawl Stars',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '2. Start the overlay service from the Dashboard',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '3. Open Brawl Stars and enter a ranked match',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '4. The overlay will automatically detect the draft state and provide recommendations',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 8),
              Text(
                '5. Tap the overlay to expand and see detailed recommendations',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

/// Dashboard tab showing main controls and status
class DashboardTab extends ConsumerWidget {
  const DashboardTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch overlay status
    final overlayStatus = ref.watch(overlayStatusProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          MdiIcons.circle,
                          color: overlayStatus.when(
                            data: (isActive) => isActive
                                ? AppTheme.successColor
                                : AppTheme.errorColor,
                            loading: () => AppTheme.warningColor,
                            error: (_, __) => AppTheme.errorColor,
                          ),
                          size: 12,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          overlayStatus.when(
                            data: (isActive) => isActive
                                ? 'Overlay Status: Running'
                                : 'Overlay Status: Stopped',
                            loading: () => 'Overlay Status: Loading...',
                            error: (_, __) => 'Overlay Status: Error',
                          ),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      overlayStatus.when(
                        data: (isActive) => isActive
                            ? 'The draft helper overlay is running and ready to assist.'
                            : 'The draft helper overlay is currently not running.',
                        loading: () => 'Checking overlay status...',
                        error: (_, __) => 'Error checking overlay status.',
                      ),
                      style: const TextStyle(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Main Action Button
            ElevatedButton.icon(
              onPressed: () async {
                final overlayService = ref.read(overlayServiceProvider);
                final isActive = ref.read(isOverlayActiveProvider);

                if (isActive) {
                  // Stop overlay
                  final success = await overlayService.stopOverlay();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          success
                              ? 'Draft helper stopped'
                              : 'Failed to stop draft helper',
                        ),
                        backgroundColor: success
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                      ),
                    );
                  }
                } else {
                  // Check permissions first
                  final permissionService = ref.read(permissionServiceProvider);
                  final hasPermission = await permissionService
                      .hasOverlayPermission();
                  if (!hasPermission) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Overlay permission required'),
                          backgroundColor: AppTheme.warningColor,
                        ),
                      );
                    }
                    return;
                  }

                  // Start overlay
                  final success = await overlayService.startOverlay();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          success
                              ? 'Draft helper started'
                              : 'Failed to start draft helper',
                        ),
                        backgroundColor: success
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                      ),
                    );
                  }
                }
              },
              icon: Icon(
                overlayStatus.when(
                  data: (isActive) => isActive ? MdiIcons.stop : MdiIcons.play,
                  loading: () => MdiIcons.loading,
                  error: (_, __) => MdiIcons.alertCircle,
                ),
              ),
              label: Text(
                overlayStatus.when(
                  data: (isActive) =>
                      isActive ? 'Stop Draft Helper' : 'Start Draft Helper',
                  loading: () => 'Loading...',
                  error: (_, __) => 'Error',
                ),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),

            // Quick Actions
            const Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final permissionService = ref.read(
                        permissionServiceProvider,
                      );
                      final hasAll = await permissionService
                          .hasAllPermissions();

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              hasAll
                                  ? 'All permissions granted'
                                  : 'Some permissions missing',
                            ),
                            backgroundColor: hasAll
                                ? AppTheme.successColor
                                : AppTheme.warningColor,
                          ),
                        );
                      }

                      if (!hasAll) {
                        // Request missing permissions
                        await permissionService.requestAllPermissions();
                      }
                    },
                    icon: Icon(MdiIcons.shield),
                    label: const Text('Check Permissions'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final overlayService = ref.read(overlayServiceProvider);
                      final status = await overlayService.getOverlayStatus();

                      if (context.mounted) {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Overlay Status'),
                            content: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text('Active: ${status['isActive']}'),
                                  Text(
                                    'Has Permission: ${status['hasPermission']}',
                                  ),
                                  Text('Supported: ${status['isSupported']}'),
                                  Text('Can Show: ${status['canShow']}'),
                                ],
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text('OK'),
                              ),
                            ],
                          ),
                        );
                      }
                    },
                    icon: Icon(MdiIcons.testTube),
                    label: const Text('Test Overlay'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Info Section
            const Text(
              'Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(MdiIcons.information, color: AppTheme.infoColor),
                        const SizedBox(width: 12),
                        const Text(
                          'Getting Started',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'To use Brawl Drafter, you need to grant overlay permission. This allows the app to show a floating widget over Brawl Stars during ranked matches.',
                      style: TextStyle(color: AppTheme.secondaryTextColor),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Settings tab for app configuration
class SettingsTab extends ConsumerWidget {
  const SettingsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Settings panel coming soon!\n\nThis will include options for:\n• Overlay appearance\n• Detection sensitivity\n• Recommendation preferences\n• Performance settings',
                style: TextStyle(color: AppTheme.secondaryTextColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// About tab with app information
class AboutTab extends ConsumerWidget {
  const AboutTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'About',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    AppConstants.appName,
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'Version ${AppConstants.appVersion}',
                    style: TextStyle(color: AppTheme.secondaryTextColor),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    AppConstants.appDescription,
                    style: TextStyle(color: AppTheme.secondaryTextColor),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Features:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• Real-time draft state detection\n• Intelligent pick/ban recommendations\n• Floating overlay interface\n• Minimal performance impact\n• Manual override capabilities',
                    style: TextStyle(color: AppTheme.secondaryTextColor),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
