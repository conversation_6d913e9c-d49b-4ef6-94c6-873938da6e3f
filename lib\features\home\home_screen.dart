import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../core/constants/app_constants.dart';
import '../../core/constants/app_theme.dart';

/// Main home screen for the Brawl Drafter app
class HomeScreen extends ConsumerStatefulWidget {
    const HomeScreen({super.key});

    @override
    ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
    int _selectedIndex = 0;

    final List<Widget> _screens = [
        const DashboardTab(),
        const SettingsTab(),
        const AboutTab(),
    ];

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            appBar: AppBar(
                title: const Text(AppConstants.appName),
                centerTitle: true,
                elevation: 0,
                actions: [
                    IconButton(
                        icon: Icon(MdiIcons.help),
                        onPressed: () {
                            _showHelpDialog(context);
                        },
                    ),
                ],
            ),
            body: _screens[_selectedIndex],
            bottomNavigationBar: BottomNavigationBar(
                currentIndex: _selectedIndex,
                onTap: (index) {
                    setState(() {
                        _selectedIndex = index;
                    });
                },
                type: BottomNavigationBarType.fixed,
                backgroundColor: AppTheme.surfaceColor,
                selectedItemColor: AppTheme.primaryColor,
                unselectedItemColor: AppTheme.secondaryTextColor,
                items: [
                    BottomNavigationBarItem(
                        icon: Icon(MdiIcons.viewDashboard),
                        label: 'Dashboard',
                    ),
                    BottomNavigationBarItem(
                        icon: Icon(MdiIcons.cog),
                        label: 'Settings',
                    ),
                    BottomNavigationBarItem(
                        icon: Icon(MdiIcons.information),
                        label: 'About',
                    ),
                ],
            ),
        );
    }

    void _showHelpDialog(BuildContext context) {
        showDialog(
            context: context,
            builder: (context) => AlertDialog(
                title: const Text('How to Use Brawl Drafter'),
                content: const SingleChildScrollView(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                            Text(
                                '1. Grant overlay permission to show floating widget over Brawl Stars',
                                style: TextStyle(fontSize: 14),
                            ),
                            SizedBox(height: 8),
                            Text(
                                '2. Start the overlay service from the Dashboard',
                                style: TextStyle(fontSize: 14),
                            ),
                            SizedBox(height: 8),
                            Text(
                                '3. Open Brawl Stars and enter a ranked match',
                                style: TextStyle(fontSize: 14),
                            ),
                            SizedBox(height: 8),
                            Text(
                                '4. The overlay will automatically detect the draft state and provide recommendations',
                                style: TextStyle(fontSize: 14),
                            ),
                            SizedBox(height: 8),
                            Text(
                                '5. Tap the overlay to expand and see detailed recommendations',
                                style: TextStyle(fontSize: 14),
                            ),
                        ],
                    ),
                ),
                actions: [
                    TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Got it'),
                    ),
                ],
            ),
        );
    }
}

/// Dashboard tab showing main controls and status
class DashboardTab extends ConsumerWidget {
    const DashboardTab({super.key});

    @override
    Widget build(BuildContext context, WidgetRef ref) {
        return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                    // Status Card
                    Card(
                        child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                    Row(
                                        children: [
                                            Icon(
                                                MdiIcons.circle,
                                                color: AppTheme.errorColor,
                                                size: 12,
                                            ),
                                            const SizedBox(width: 8),
                                            const Text(
                                                'Overlay Status: Stopped',
                                                style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                ),
                                            ),
                                        ],
                                    ),
                                    const SizedBox(height: 8),
                                    const Text(
                                        'The draft helper overlay is currently not running.',
                                        style: TextStyle(
                                            color: AppTheme.secondaryTextColor,
                                        ),
                                    ),
                                ],
                            ),
                        ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Main Action Button
                    ElevatedButton.icon(
                        onPressed: () {
                            // TODO: Implement overlay start/stop
                            ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text('Overlay functionality coming soon!'),
                                ),
                            );
                        },
                        icon: Icon(MdiIcons.play),
                        label: const Text('Start Draft Helper'),
                        style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Quick Actions
                    const Text(
                        'Quick Actions',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                        ),
                    ),
                    const SizedBox(height: 12),
                    
                    Row(
                        children: [
                            Expanded(
                                child: OutlinedButton.icon(
                                    onPressed: () {
                                        // TODO: Implement permission check
                                        ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                                content: Text('Permission check coming soon!'),
                                            ),
                                        );
                                    },
                                    icon: Icon(MdiIcons.shield),
                                    label: const Text('Check Permissions'),
                                ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                                child: OutlinedButton.icon(
                                    onPressed: () {
                                        // TODO: Implement test overlay
                                        ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                                content: Text('Test overlay coming soon!'),
                                            ),
                                        );
                                    },
                                    icon: Icon(MdiIcons.testTube),
                                    label: const Text('Test Overlay'),
                                ),
                            ),
                        ],
                    ),
                    const SizedBox(height: 24),
                    
                    // Info Section
                    const Text(
                        'Information',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                        ),
                    ),
                    const SizedBox(height: 12),
                    
                    Card(
                        child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                    Row(
                                        children: [
                                            Icon(
                                                MdiIcons.information,
                                                color: AppTheme.infoColor,
                                            ),
                                            const SizedBox(width: 12),
                                            const Text(
                                                'Getting Started',
                                                style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                ),
                                            ),
                                        ],
                                    ),
                                    const SizedBox(height: 8),
                                    const Text(
                                        'To use Brawl Drafter, you need to grant overlay permission. This allows the app to show a floating widget over Brawl Stars during ranked matches.',
                                        style: TextStyle(
                                            color: AppTheme.secondaryTextColor,
                                        ),
                                    ),
                                ],
                            ),
                        ),
                    ),
                ],
            ),
        );
    }
}

/// Settings tab for app configuration
class SettingsTab extends ConsumerWidget {
    const SettingsTab({super.key});

    @override
    Widget build(BuildContext context, WidgetRef ref) {
        return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                    Text(
                        'Settings',
                        style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                        ),
                    ),
                    SizedBox(height: 16),
                    Card(
                        child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Text(
                                'Settings panel coming soon!\n\nThis will include options for:\n• Overlay appearance\n• Detection sensitivity\n• Recommendation preferences\n• Performance settings',
                                style: TextStyle(
                                    color: AppTheme.secondaryTextColor,
                                ),
                            ),
                        ),
                    ),
                ],
            ),
        );
    }
}

/// About tab with app information
class AboutTab extends ConsumerWidget {
    const AboutTab({super.key});

    @override
    Widget build(BuildContext context, WidgetRef ref) {
        return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                    const Text(
                        'About',
                        style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                        ),
                    ),
                    const SizedBox(height: 16),
                    
                    Card(
                        child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                    const Text(
                                        AppConstants.appName,
                                        style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w600,
                                        ),
                                    ),
                                    const SizedBox(height: 4),
                                    const Text(
                                        'Version ${AppConstants.appVersion}',
                                        style: TextStyle(
                                            color: AppTheme.secondaryTextColor,
                                        ),
                                    ),
                                    const SizedBox(height: 12),
                                    const Text(
                                        AppConstants.appDescription,
                                        style: TextStyle(
                                            color: AppTheme.secondaryTextColor,
                                        ),
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                        'Features:',
                                        style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                        ),
                                    ),
                                    const SizedBox(height: 8),
                                    const Text(
                                        '• Real-time draft state detection\n• Intelligent pick/ban recommendations\n• Floating overlay interface\n• Minimal performance impact\n• Manual override capabilities',
                                        style: TextStyle(
                                            color: AppTheme.secondaryTextColor,
                                        ),
                                    ),
                                ],
                            ),
                        ),
                    ),
                ],
            ),
        );
    }
}
