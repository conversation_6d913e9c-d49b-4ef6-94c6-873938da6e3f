import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';

import '../../core/constants/app_theme.dart';
import '../../shared/providers/app_providers.dart';
import 'widgets/overlay_icon.dart';
import 'widgets/overlay_expanded.dart';

/// Entry point for the floating overlay window
/// This is the main widget that gets displayed over other apps
void overlayMain() {
    runApp(
        const ProviderScope(
            child: OverlayApp(),
        ),
    );
}

/// Main overlay application widget
class OverlayApp extends StatelessWidget {
    const OverlayApp({super.key});

    @override
    Widget build(BuildContext context) {
        return MaterialApp(
            title: 'Brawl Drafter Overlay',
            theme: AppTheme.overlayTheme,
            debugShowCheckedModeBanner: false,
            home: const OverlayWindow(),
        );
    }
}

/// Main overlay window that handles collapsed/expanded states
class OverlayWindow extends ConsumerStatefulWidget {
    const OverlayWindow({super.key});

    @override
    ConsumerState<OverlayWindow> createState() => _OverlayWindowState();
}

class _OverlayWindowState extends ConsumerState<OverlayWindow>
    with TickerProviderStateMixin {
    bool _isExpanded = false;
    late AnimationController _animationController;
    late Animation<double> _scaleAnimation;
    late Animation<double> _opacityAnimation;

    @override
    void initState() {
        super.initState();
        
        // Initialize animations
        _animationController = AnimationController(
            duration: const Duration(milliseconds: 300),
            vsync: this,
        );
        
        _scaleAnimation = Tween<double>(
            begin: 1.0,
            end: 1.1,
        ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.elasticOut,
        ));
        
        _opacityAnimation = Tween<double>(
            begin: 0.9,
            end: 1.0,
        ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
        ));

        // Listen for data from main app
        _setupDataListener();
    }

    @override
    void dispose() {
        _animationController.dispose();
        super.dispose();
    }

    /// Set up listener for data from main app
    void _setupDataListener() {
        FlutterOverlayWindow.overlayListener.listen((data) {
            if (mounted && data is Map<String, dynamic>) {
                _handleDataFromMainApp(data);
            }
        });
    }

    /// Handle data received from main app
    void _handleDataFromMainApp(Map<String, dynamic> data) {
        final action = data['action'] as String?;
        
        switch (action) {
            case 'expand':
                _expandOverlay();
                break;
            case 'collapse':
                _collapseOverlay();
                break;
            case 'update_draft':
                _updateDraftData(data);
                break;
            case 'update_recommendations':
                _updateRecommendations(data);
                break;
            default:
                debugPrint('Unknown overlay action: $action');
        }
    }

    /// Expand the overlay to show full interface
    void _expandOverlay() {
        if (!_isExpanded) {
            setState(() {
                _isExpanded = true;
            });
            _animationController.forward();
        }
    }

    /// Collapse the overlay to icon only
    void _collapseOverlay() {
        if (_isExpanded) {
            setState(() {
                _isExpanded = false;
            });
            _animationController.reverse();
        }
    }

    /// Toggle between expanded and collapsed states
    void _toggleOverlay() {
        if (_isExpanded) {
            _collapseOverlay();
        } else {
            _expandOverlay();
        }
    }

    /// Update draft data from main app
    void _updateDraftData(Map<String, dynamic> data) {
        // Update draft state in overlay
        // This would typically update the draft state provider
        debugPrint('Updating draft data: $data');
    }

    /// Update recommendations from main app
    void _updateRecommendations(Map<String, dynamic> data) {
        // Update recommendations in overlay
        debugPrint('Updating recommendations: $data');
    }

    /// Send data back to main app
    void _sendDataToMainApp(Map<String, dynamic> data) {
        FlutterOverlayWindow.shareData(data);
    }

    /// Handle overlay tap
    void _onOverlayTap() {
        _toggleOverlay();
        
        // Send action to main app
        _sendDataToMainApp({
            'action': _isExpanded ? 'expanded' : 'collapsed',
            'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
    }

    /// Handle close overlay
    void _onCloseOverlay() {
        _sendDataToMainApp({
            'action': 'close',
            'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: Colors.transparent,
            body: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                    return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Opacity(
                            opacity: _opacityAnimation.value,
                            child: _isExpanded
                                ? OverlayExpanded(
                                    onClose: _onCloseOverlay,
                                    onCollapse: _collapseOverlay,
                                    onSendData: _sendDataToMainApp,
                                )
                                : OverlayIcon(
                                    onTap: _onOverlayTap,
                                    onLongPress: _onCloseOverlay,
                                ),
                        ),
                    );
                },
            ),
        );
    }
}
