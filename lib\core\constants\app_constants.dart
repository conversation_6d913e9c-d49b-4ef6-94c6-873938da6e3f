/// Application-wide constants for Brawl Drafter
class AppConstants {
    // App Information
    static const String appName = 'Brawl Drafter';
    static const String appVersion = '1.0.0';
    static const String appDescription = 'Real-time draft assistance for Brawl Stars';

    // Overlay Configuration
    static const double overlayIconSize = 60.0;
    static const double overlayExpandedWidth = 320.0;
    static const double overlayExpandedHeight = 480.0;
    static const double overlayCornerRadius = 12.0;
    static const double overlayOpacity = 0.95;

    // Screen Capture Settings
    static const int screenCaptureIntervalMs = 2000; // 2 seconds
    static const int maxScreenCaptureRetries = 3;
    static const double imageMatchThreshold = 0.8;

    // Draft Timing
    static const int banPhaseDurationSeconds = 30;
    static const int pickPhaseDurationSeconds = 30;
    static const int draftTimeoutSeconds = 300; // 5 minutes total

    // Recommendation Settings
    static const int maxPickRecommendations = 5;
    static const int maxBanRecommendations = 3;
    static const double minRecommendationScore = 60.0;

    // Performance Settings
    static const int maxCpuUsagePercent = 5;
    static const int maxMemoryUsageMB = 100;
    static const int backgroundProcessingIntervalMs = 1000;

    // Asset Paths
    static const String brawlerIconsPath = 'assets/images/brawlers/';
    static const String mapIconsPath = 'assets/images/maps/';
    static const String uiIconsPath = 'assets/images/ui/';
    static const String dataPath = 'assets/data/';

    // File Extensions
    static const String imageExtension = '.png';
    static const String dataExtension = '.json';

    // Storage Keys
    static const String userPreferencesKey = 'user_preferences';
    static const String draftHistoryKey = 'draft_history';
    static const String brawlerDataKey = 'brawler_data';
    static const String overlayPositionKey = 'overlay_position';

    // Network Settings
    static const int httpTimeoutSeconds = 10;
    static const int maxRetryAttempts = 3;
    static const String apiBaseUrl = 'https://api.brawlstats.com/v1';

    // UI Colors (Material Design)
    static const int primaryColorValue = 0xFF1976D2; // Blue
    static const int accentColorValue = 0xFFFF5722; // Deep Orange
    static const int backgroundColorValue = 0xFF121212; // Dark
    static const int surfaceColorValue = 0xFF1E1E1E; // Dark Surface
    static const int errorColorValue = 0xFFCF6679; // Error Red

    // Animation Durations
    static const int shortAnimationMs = 200;
    static const int mediumAnimationMs = 300;
    static const int longAnimationMs = 500;

    // Logging
    static const String logFileName = 'brawl_drafter.log';
    static const int maxLogFileSizeMB = 10;
    static const int maxLogFiles = 5;

    // Permissions
    static const List<String> requiredPermissions = [
        'android.permission.SYSTEM_ALERT_WINDOW',
        'android.permission.FOREGROUND_SERVICE',
        'android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION',
    ];

    // Error Messages
    static const String permissionDeniedError = 'Required permissions not granted';
    static const String screenCaptureError = 'Failed to capture screen';
    static const String overlayError = 'Failed to show overlay';
    static const String detectionError = 'Failed to detect game state';
    static const String networkError = 'Network connection failed';

    // Success Messages
    static const String overlayStartedMessage = 'Draft helper overlay started';
    static const String overlayStoppedMessage = 'Draft helper overlay stopped';
    static const String permissionsGrantedMessage = 'All permissions granted';

    // Feature Flags
    static const bool enableAdvancedRecommendations = false;
    static const bool enableTelemetry = false;
    static const bool enableDebugMode = false;
    static const bool enableAutoDetection = true;
    static const bool enableManualOverride = true;

    // Template Matching
    static const double templateMatchThreshold = 0.85;
    static const int templateMatchMaxResults = 5;
    static const double templateScaleTolerance = 0.1;

    // Draft Flow Configuration
    static const List<String> banPhaseOrder = [
        'Team A Player 1',
        'Team B Player 1', 
        'Team A Player 2',
        'Team B Player 2',
        'Team A Player 3',
        'Team B Player 3',
    ];

    static const List<String> pickPhaseOrder = [
        'Team A Player 1',
        'Team B Player 1',
        'Team B Player 2',
        'Team A Player 2',
        'Team A Player 3',
        'Team B Player 3',
    ];

    // Map Categories
    static const List<String> gemGrabMaps = [
        'Hard Rock Mine',
        'Crystal Arcade',
        'Minecart Madness',
    ];

    static const List<String> brawlBallMaps = [
        'Sneaky Fields',
        'Super Stadium',
        'Penalty Kick',
    ];

    static const List<String> bountyMaps = [
        'Dry Season',
        'Snake Prairie',
        'Shooting Star',
    ];

    static const List<String> heistMaps = [
        'Safe Zone',
        'Kaboom Canyon',
        'Hot Potato',
    ];

    static const List<String> siegeMaps = [
        'Factory Rush',
        'Nuts & Bolts',
        'Junk Park',
    ];

    // Brawler Categories for Quick Access
    static const List<String> tankBrawlers = [
        'Bull', 'El Primo', 'Rosa', 'Darryl', 'Frank', 'Bibi', 'Jacky', 'Ash'
    ];

    static const List<String> supportBrawlers = [
        'Poco', 'Pam', 'Gene', 'Max', 'Byron', 'Ruffs', 'Belle'
    ];

    static const List<String> sniperBrawlers = [
        'Brock', 'Piper', 'Belle', 'Byron', 'Mandy'
    ];
}
