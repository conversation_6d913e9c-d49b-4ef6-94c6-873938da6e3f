import 'package:permission_handler/permission_handler.dart';
// ...existing code...

import '../../main.dart';
// ...existing code...

/// Service for handling Android permissions required by the app
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Check if all required permissions are granted
  Future<bool> hasAllPermissions() async {
    try {
      // Check overlay permission (special case)
      final hasOverlayPermission = await Permission.systemAlertWindow.isGranted;

      // Check other permissions
      final permissions = [
        Permission.camera, // For screen capture
        Permission.storage,
        Permission.notification,
      ];

      final statuses = await permissions.request();
      final allGranted = statuses.values.every((status) => status.isGranted);

      logger.d('Overlay permission: $hasOverlayPermission');
      logger.d('Other permissions: $allGranted');

      return hasOverlayPermission && allGranted;
    } catch (e) {
      logger.e('Error checking permissions: $e');
      return false;
    }
  }

  /// Request all required permissions
  Future<bool> requestAllPermissions() async {
    try {
      logger.i('Requesting permissions...');

      // Request overlay permission first (special handling required)
      final overlayStatus = await Permission.systemAlertWindow.request();

      if (!overlayStatus.isGranted) {
        logger.w('Overlay permission denied');
        return false;
      }

      // Request other permissions
      final permissions = [
        Permission.camera,
        Permission.storage,
        Permission.notification,
      ];

      final statuses = await permissions.request();

      // Check if all permissions were granted
      final allGranted = statuses.values.every((status) => status.isGranted);

      if (allGranted) {
        logger.i('All permissions granted successfully');
        return true;
      } else {
        logger.w('Some permissions were denied');
        _logPermissionStatuses(statuses);
        return false;
      }
    } catch (e) {
      logger.e('Error requesting permissions: $e');
      return false;
    }
  }

  /// Check specific permission status
  Future<PermissionStatus> checkPermission(Permission permission) async {
    try {
      return await permission.status;
    } catch (e) {
      logger.e('Error checking permission $permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Request specific permission
  Future<PermissionStatus> requestPermission(Permission permission) async {
    try {
      return await permission.request();
    } catch (e) {
      logger.e('Error requesting permission $permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Check if overlay permission is granted
  Future<bool> hasOverlayPermission() async {
    try {
      return await Permission.systemAlertWindow.isGranted;
    } catch (e) {
      logger.e('Error checking overlay permission: $e');
      return false;
    }
  }

  /// Request overlay permission
  Future<bool> requestOverlayPermission() async {
    try {
      final status = await Permission.systemAlertWindow.request();

      if (status.isGranted) {
        logger.i('Overlay permission granted');
        return true;
      } else if (status.isPermanentlyDenied) {
        logger.w('Overlay permission permanently denied');
        await openAppSettings();
        return false;
      } else {
        logger.w('Overlay permission denied');
        return false;
      }
    } catch (e) {
      logger.e('Error requesting overlay permission: $e');
      return false;
    }
  }

  /// Open app settings for manual permission configuration
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      logger.e('Error opening app settings: $e');
      return false;
    }
  }

  /// Get permission status description
  String getPermissionStatusDescription(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.restricted:
        return 'Restricted';
      case PermissionStatus.limited:
        return 'Limited';
      case PermissionStatus.permanentlyDenied:
        return 'Permanently Denied';
      case PermissionStatus.provisional:
        return 'Provisional';
    }
  }

  /// Get all permission statuses for debugging
  Future<Map<String, PermissionStatus>> getAllPermissionStatuses() async {
    final permissions = [
      Permission.systemAlertWindow,
      Permission.camera,
      Permission.storage,
      Permission.notification,
    ];

    final Map<String, PermissionStatus> statuses = {};

    for (final permission in permissions) {
      try {
        statuses[permission.toString()] = await permission.status;
      } catch (e) {
        logger.e('Error getting status for $permission: $e');
        statuses[permission.toString()] = PermissionStatus.denied;
      }
    }

    return statuses;
  }

  /// Log permission statuses for debugging
  void _logPermissionStatuses(Map<Permission, PermissionStatus> statuses) {
    logger.d('Permission statuses:');
    statuses.forEach((permission, status) {
      logger.d('  $permission: ${getPermissionStatusDescription(status)}');
    });
  }

  /// Check if the app can show overlay
  Future<bool> canShowOverlay() async {
    try {
      // Check if overlay permission is granted
      final hasPermission = await hasOverlayPermission();

      if (!hasPermission) {
        logger.w('Cannot show overlay: permission not granted');
        return false;
      }

      // Additional checks can be added here
      // (e.g., battery optimization, do not disturb mode, etc.)

      return true;
    } catch (e) {
      logger.e('Error checking if can show overlay: $e');
      return false;
    }
  }

  /// Get user-friendly permission explanation
  String getPermissionExplanation(Permission permission) {
    switch (permission) {
      case Permission.systemAlertWindow:
        return 'Required to show the draft helper overlay on top of Brawl Stars';
      case Permission.camera:
        return 'Required to capture screen content for draft detection';
      case Permission.storage:
        return 'Required to save brawler data and user preferences';
      case Permission.notification:
        return 'Required to show draft notifications and updates';
      default:
        return 'Required for app functionality';
    }
  }

  /// Check if permission is critical (app cannot function without it)
  bool isPermissionCritical(Permission permission) {
    const criticalPermissions = [Permission.systemAlertWindow];

    return criticalPermissions.contains(permission);
  }
}
