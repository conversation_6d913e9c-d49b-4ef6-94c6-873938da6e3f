// Basic Flutter widget test for Brawl Drafter app.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:brawldrafter/main.dart';

void main() {
  testWidgets('App loads and shows home screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: BrawlDrafterApp()));

    // Verify that the app title is displayed.
    expect(find.text('Brawl Drafter'), findsOneWidget);

    // Verify that the dashboard tab is shown by default.
    expect(find.text('Dashboard'), findsOneWidget);

    // Verify that the main action button is present.
    expect(find.text('Start Draft Helper'), findsOneWidget);
  });

  testWidgets('Navigation between tabs works', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: BrawlDrafterApp()));

    // Tap on Settings tab.
    await tester.tap(find.text('Settings'));
    await tester.pump();

    // Verify that Settings content is shown.
    expect(find.text('Settings panel coming soon!'), findsOneWidget);

    // Tap on About tab.
    await tester.tap(find.text('About'));
    await tester.pump();

    // Verify that About content is shown.
    expect(find.text('Version 1.0.0'), findsOneWidget);
  });
}
